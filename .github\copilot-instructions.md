<!-- Use this file to provide workspace-specific custom instructions to <PERSON><PERSON><PERSON>. For more details, visit https://code.visualstudio.com/docs/copilot/copilot-customization#_use-a-githubcopilotinstructionsmd-file -->

This project is a Vite + JavaScript (vanilla) frontend app. It features a single page with a record button, waveform visualization, noise suppression, echo cancellation, and saves audio as a 16kHz WAV file in the 'recordings' folder. When recording starts, a specific text is displayed to the user.
