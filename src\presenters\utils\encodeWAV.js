export function encodeWAV(audioBuffer, targetSampleRate) {
  // Validation
  if (!audioBuffer || typeof targetSampleRate !== 'number' || targetSampleRate <= 0) {
    throw new Error('Invalid audio buffer or target sample rate');
  }

  const numChannels = Math.min(audioBuffer.numberOfChannels, 2); // Limit to stereo
  const samples = downsampleBuffer(audioBuffer, targetSampleRate, numChannels);
  const buffer = new ArrayBuffer(44 + samples.length * 2);
  const view = new DataView(buffer);

  // WAV header
  writeString(view, 0, 'RIFF');
  view.setUint32(4, 36 + samples.length * 2, true);
  writeString(view, 8, 'WAVE');
  writeString(view, 12, 'fmt ');
  view.setUint32(16, 16, true); // PCM format
  view.setUint16(20, 1, true); // Audio format (1 = PCM)
  view.setUint16(22, numChannels, true);
  view.setUint32(24, targetSampleRate, true);
  view.setUint32(28, targetSampleRate * numChannels * 2, true); // Byte rate
  view.setUint16(32, numChannels * 2, true); // Block align
  view.setUint16(34, 16, true); // Bits per sample
  writeString(view, 36, 'data');
  view.setUint32(40, samples.length * 2, true);

  // Convert samples to 16-bit PCM
  let offset = 44;
  for (let i = 0; i < samples.length; i++, offset += 2) {
    let s = Math.max(-1, Math.min(1, samples[i]));
    view.setInt16(offset, s < 0 ? s * 0x8000 : s * 0x7FFF, true);
  }

  return new Blob([view], { type: 'audio/wav' });
}
function writeString(view, offset, string) {
  for (let i = 0; i < string.length; i++) {
    view.setUint8(offset + i, string.charCodeAt(i));
  }
}
function downsampleBuffer(buffer, targetSampleRate, numChannels = 1) {
  if (buffer.sampleRate === targetSampleRate) {
    if (numChannels === 1) {
      return buffer.getChannelData(0);
    } else {
      // Interleave channels for stereo
      const left = buffer.getChannelData(0);
      const right = buffer.numberOfChannels > 1 ? buffer.getChannelData(1) : left;
      const result = new Float32Array(left.length * 2);
      for (let i = 0; i < left.length; i++) {
        result[i * 2] = left[i];
        result[i * 2 + 1] = right[i];
      }
      return result;
    }
  }

  const ratio = buffer.sampleRate / targetSampleRate;
  const newLength = Math.round(buffer.getChannelData(0).length / ratio);

  if (numChannels === 1) {
    // Mono: mix all channels or use first channel
    const channel = buffer.getChannelData(0);
    const result = new Float32Array(newLength);
    for (let i = 0; i < newLength; i++) {
      const sourceIndex = Math.round(i * ratio);
      result[i] = channel[sourceIndex];
    }
    return result;
  } else {
    // Stereo: interleave left and right channels
    const left = buffer.getChannelData(0);
    const right = buffer.numberOfChannels > 1 ? buffer.getChannelData(1) : left;
    const result = new Float32Array(newLength * 2);

    for (let i = 0; i < newLength; i++) {
      const sourceIndex = Math.round(i * ratio);
      result[i * 2] = left[sourceIndex];
      result[i * 2 + 1] = right[sourceIndex];
    }
    return result;
  }
}
