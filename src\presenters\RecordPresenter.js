import { encodeWAV } from './utils/encodeWAV.js';

export class RecordPresenter {
  constructor(model, view, dom) {
    this.model = model;
    this.view = view;
    this.dom = dom;
    this.mediaRecorder = null;
    this.audioContext = null;
    this.analyser = null;
    this.dataArray = null;
    this.source = null;
    this.animationId = null;
    this.stream = null;
    this.recordingCount = 0;
    this.lastFrameTime = 0;
    this.supportedMimeTypes = this.getSupportedMimeTypes();
  }

  // Check browser support for audio formats
  getSupportedMimeTypes() {
    const types = [
      'audio/webm;codecs=opus',
      'audio/webm',
      'audio/mp4',
      'audio/ogg;codecs=opus',
      'audio/wav'
    ];

    return types.filter(type => MediaRecorder.isTypeSupported(type));
  }

  // Validate browser capabilities
  validateBrowserSupport() {
    const issues = [];

    if (!navigator.mediaDevices) {
      issues.push('MediaDevices API not supported');
    }

    if (!navigator.mediaDevices.getUserMedia) {
      issues.push('getUserMedia not supported');
    }

    if (!window.MediaRecorder) {
      issues.push('MediaRecorder not supported');
    }

    if (!window.AudioContext && !window['webkitAudioContext']) {
      issues.push('Web Audio API not supported');
    }

    if (this.supportedMimeTypes.length === 0) {
      issues.push('No supported audio formats found');
    }

    return {
      isSupported: issues.length === 0,
      issues: issues,
      supportedFormats: this.supportedMimeTypes
    };
  }

  // Utility function to save file to recordings folder (using File System Access API if available)
  async saveRecordingFile(blob, filename) {
    try {
      // Check if File System Access API is supported
      if ('showSaveFilePicker' in window) {
        const fileHandle = await window.showSaveFilePicker({
          suggestedName: filename,
          startIn: 'downloads',
          types: [{
            description: 'WAV files',
            accept: { 'audio/wav': ['.wav'] }
          }]
        });
        const writable = await fileHandle.createWritable();
        await writable.write(blob);
        await writable.close();
        return true;
      } else {
        // Fallback: trigger download
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        return false;
      }
    } catch (error) {
      console.error('Error saving file:', error);
      return false;
    }
  }

  // Enhanced error handling
  handleError(error, context) {
    console.error(`Error in ${context}:`, error);
    let userMessage = 'An unexpected error occurred.';
    
    switch (context) {
      case 'microphone':
        if (error.name === 'NotAllowedError') {
          userMessage = 'Microphone access denied. Please allow microphone access and try again.';
        } else if (error.name === 'NotFoundError') {
          userMessage = 'No microphone found. Please connect a microphone and try again.';
        } else {
          userMessage = 'Error accessing microphone. Please check your microphone settings.';
        }
        break;
      case 'encoding':
        userMessage = 'Error processing audio. Please try recording again.';
        break;
      case 'network':
        userMessage = 'Network error. Please check your connection and try again.';
        break;
      case 'api':
        userMessage = 'Server error. Accent prediction is temporarily unavailable.';
        break;
    }
    
    this.dom.accentResult.style.display = 'block';
    this.dom.accentResult.textContent = userMessage;
    this.model.setError(error);
  }

  init() {
    // Check browser support first
    const support = this.validateBrowserSupport();
    if (!support.isSupported) {
      console.error('Browser compatibility issues:', support.issues);
      if (this.dom.accentResult) {
        this.dom.accentResult.style.display = 'block';
        this.dom.accentResult.textContent = `Browser not supported: ${support.issues.join(', ')}`;
      }
      return;
    }

    console.log('Supported audio formats:', support.supportedFormats);

    // Defensive: Only add event if all DOM refs exist
    if (
      this.dom.recordBtn &&
      this.dom.audioPlayer &&
      this.dom.downloadLink &&
      this.dom.accentResult &&
      this.dom.recordText &&
      this.dom.waveform
    ) {
      this.dom.recordBtn.onclick = () => this.handleRecordClick();
    } else {
      console.error('One or more DOM elements are missing:', this.dom);
    }
  }

  async handleRecordClick() {
    // Defensive: check all DOM refs before using
    if (!this.dom.audioPlayer || !this.dom.downloadLink || !this.dom.accentResult) {
      console.error('DOM references missing:', this.dom);
      return;
    }
    
    // Clear previous results
    this.dom.audioPlayer.src = '';
    this.dom.audioPlayer.style.display = 'none';
    this.dom.downloadLink.style.display = 'none';
    this.dom.accentResult.style.display = 'none';
    this.dom.accentResult.textContent = '';

    // Stop recording if currently recording
    if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {
      this.stopRecording();
      return;
    }

    // Start recording
    await this.startRecording();
  }

  stopRecording() {
    this.mediaRecorder.stop();
    this.dom.recordBtn.textContent = 'Start Recording';
    this.dom.recordText.style.display = 'none';
    this.dom.waveform.style.display = 'none';
    
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
      this.animationId = null;
    }
    
    if (this.audioContext) {
      this.audioContext.close();
      this.audioContext = null;
    }
    
    if (this.stream) {
      this.stream.getTracks().forEach(track => track.stop());
      this.stream = null;
    }
  }

  async startRecording() {
    try {
      // Check browser support
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error('MediaDevices API not supported');
      }

      this.stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          noiseSuppression: true,
          echoCancellation: true,
          sampleRate: 16000
        }
      });
      
      this.model.clearAudioChunks();
      
      // Use the best supported mime type
      const mimeType = this.supportedMimeTypes[0] || 'audio/webm';
      this.mediaRecorder = new MediaRecorder(this.stream, { mimeType });
      
      this.mediaRecorder.ondataavailable = e => {
        if (e.data.size > 0) {
          this.model.addAudioChunk(e.data);
        }
      };
      
      this.mediaRecorder.onstop = async () => {
        try {
          await this.processRecording();
        } catch (error) {
          this.handleError(error, 'encoding');
        }
      };

      this.mediaRecorder.onerror = (event) => {
        this.handleError(event.error, 'recording');
      };

      // Setup waveform visualization
      this.setupWaveform();
      
      // Update UI
      this.dom.recordBtn.textContent = 'Stop Recording';
      this.dom.recordText.textContent = `Elden Ring isn't just another action game—it's a test of patience, timing, and strategy. You'll die. A lot. But each defeat teaches you something. Mastering moves, reading enemy patterns, and learning when to strike or step back is key. It's not about button-mashing—it's about understanding the fight. Once you get it, every victory feels earned. This isn't a game you play; it's one you overcome.`;
      this.dom.recordText.style.display = 'block';
      this.dom.waveform.style.display = 'block';
      this.dom.downloadLink.style.display = 'none';
      
      this.mediaRecorder.start();
      this.drawWaveform();
      
    } catch (error) {
      this.handleError(error, 'microphone');
    }
  }

  setupWaveform() {
    try {
      this.audioContext = new AudioContext();
      this.source = this.audioContext.createMediaStreamSource(this.stream);
      this.analyser = this.audioContext.createAnalyser();
      this.analyser.fftSize = 2048;
      this.dataArray = new Uint8Array(this.analyser.fftSize);
      this.source.connect(this.analyser);
    } catch (error) {
      console.error('Error setting up waveform:', error);
    }
  }

  async processRecording() {
    const audioBlob = new Blob(this.model.state.audioChunks, { type: 'audio/webm' });
    
    try {
      // Convert to WAV
      const arrayBuffer = await audioBlob.arrayBuffer();
      const audioBuffer = await (new AudioContext()).decodeAudioData(arrayBuffer);
      const wavBlob = encodeWAV(audioBuffer, 16000);
      
      // Generate filename with timestamp
      this.recordingCount++;
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `recording_${timestamp}_${this.recordingCount}.wav`;
      
      // Setup audio player
      const url = URL.createObjectURL(wavBlob);
      this.dom.audioPlayer.src = url;
      this.dom.audioPlayer.style.display = 'block';
      
      // Setup download link
      this.dom.downloadLink.href = url;
      this.dom.downloadLink.download = filename;
      this.dom.downloadLink.textContent = 'Download Recording';
      this.dom.downloadLink.style.display = 'block';
      
      // Try to save file automatically
      await this.saveRecordingFile(wavBlob, filename);
      
      // Send to accent prediction API
      await this.predictAccent(wavBlob);
      
    } catch (error) {
      this.handleError(error, 'encoding');
    }
  }

  async predictAccent(wavBlob) {
    this.dom.accentResult.style.display = 'block';
    this.dom.accentResult.textContent = 'Memprediksi aksen...';
    
    try {
      const formData = new FormData();
      formData.append('file', wavBlob, 'recording.wav');
      
      const response = await fetch('http://localhost:5000/identify', {
        method: 'POST',
        body: formData
      });
      
      if (!response.ok) {
        throw new Error(`Server responded with status: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data.predicted_accent && typeof data.us_confidence === 'number') {
        if (data.predicted_accent.toLowerCase() === 'us') {
          this.dom.accentResult.textContent = `✅ Selamat! Aksen Anda terdeteksi sebagai US dengan keyakinan ${data.us_confidence.toFixed(2)}%.`;
        } else {
          this.dom.accentResult.textContent = `❌ Aksen Anda terdeteksi sebagai ${data.predicted_accent} (keyakinan US: ${data.us_confidence.toFixed(2)}%). Cobalah berbicara dengan aksen US.`;
        }
      } else {
        this.dom.accentResult.textContent = 'Gagal membaca hasil prediksi aksen.';
      }
      
    } catch (error) {
      this.handleError(error, 'api');
    }
  }

  drawWaveform() {
    if (!this.analyser || !this.dataArray) return;

    // Throttle rendering for better performance (60fps max)
    const now = performance.now();
    if (this.lastFrameTime && now - this.lastFrameTime < 16.67) {
      this.animationId = requestAnimationFrame(() => this.drawWaveform());
      return;
    }
    this.lastFrameTime = now;

    this.analyser.getByteTimeDomainData(this.dataArray);
    const ctx = this.dom.waveform.getContext('2d');
    const width = this.dom.waveform.width;
    const height = this.dom.waveform.height;
    const centerY = height / 2;

    // Clear canvas with gradient background
    const gradient = ctx.createLinearGradient(0, 0, 0, height);
    gradient.addColorStop(0, '#1a1a1a');
    gradient.addColorStop(1, '#2a2a2a');
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, width, height);

    // Calculate amplitude for dynamic coloring
    let maxAmplitude = 0;
    for (let i = 0; i < this.dataArray.length; i++) {
      const amplitude = Math.abs(this.dataArray[i] - 128);
      maxAmplitude = Math.max(maxAmplitude, amplitude);
    }

    // Dynamic color based on amplitude
    const intensity = maxAmplitude / 128;
    const hue = Math.max(200 - intensity * 80, 120); // Blue to green based on volume
    const saturation = 70 + intensity * 30;
    const lightness = 50 + intensity * 20;

    ctx.lineWidth = 2;
    ctx.strokeStyle = `hsl(${hue}, ${saturation}%, ${lightness}%)`;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';

    // Draw waveform with improved sampling
    ctx.beginPath();
    const sliceWidth = width / this.dataArray.length;
    let x = 0;

    for (let i = 0; i < this.dataArray.length; i++) {
      const v = this.dataArray[i] / 128.0;
      const y = v * centerY;

      if (i === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
      x += sliceWidth;
    }

    ctx.stroke();

    // Add center line for reference
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
    ctx.lineWidth = 1;
    ctx.beginPath();
    ctx.moveTo(0, centerY);
    ctx.lineTo(width, centerY);
    ctx.stroke();

    this.animationId = requestAnimationFrame(() => this.drawWaveform());
  }
}
