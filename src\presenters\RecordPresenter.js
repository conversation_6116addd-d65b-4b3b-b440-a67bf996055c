import { encodeWAV } from './utils/encodeWAV.js';

export class RecordPresenter {
  constructor(model, view, dom) {
    this.model = model;
    this.view = view;
    this.dom = dom;
    this.mediaRecorder = null;
    this.audioContext = null;
    this.analyser = null;
    this.dataArray = null;
    this.source = null;
    this.animationId = null;
    this.stream = null;
  }

  init() {
    // Defensive: Only add event if all DOM refs exist
    if (
      this.dom.recordBtn &&
      this.dom.audioPlayer &&
      this.dom.downloadLink &&
      this.dom.accentResult &&
      this.dom.recordText &&
      this.dom.waveform
    ) {
      this.dom.recordBtn.onclick = () => this.handleRecordClick();
    } else {
      console.error('One or more DOM elements are missing:', this.dom);
    }
  }

  async handleRecordClick() {
    // Defensive: check all DOM refs before using
    if (!this.dom.audioPlayer || !this.dom.downloadLink || !this.dom.accentResult) {
      console.error('DOM references missing:', this.dom);
      return;
    }
    // Remove previous playback and result
    this.dom.audioPlayer.src = '';
    this.dom.audioPlayer.style.display = 'none';
    this.dom.downloadLink.style.display = 'none';
    this.dom.accentResult.style.display = 'none';
    this.dom.accentResult.textContent = '';

    if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {
      this.mediaRecorder.stop();
      this.dom.recordBtn.textContent = 'Start Recording';
      this.dom.recordText.style.display = 'none';
      this.dom.waveform.style.display = 'none';
      cancelAnimationFrame(this.animationId);
      if (this.audioContext) {
        this.audioContext.close();
        this.audioContext = null;
      }
      if (this.stream) {
        this.stream.getTracks().forEach(track => track.stop());
        this.stream = null;
      }
      return;
    }
    try {
      this.stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          noiseSuppression: true,
          echoCancellation: true,
          sampleRate: 16000
        }
      });
      this.model.clearAudioChunks();
      this.mediaRecorder = new MediaRecorder(this.stream, { mimeType: 'audio/webm' });
      this.mediaRecorder.ondataavailable = e => {
        this.model.addAudioChunk(e.data);
      };
      this.mediaRecorder.onstop = async () => {
        const audioBlob = new Blob(this.model.state.audioChunks, { type: 'audio/webm' });
        const arrayBuffer = await audioBlob.arrayBuffer();
        const audioBuffer = await (new AudioContext()).decodeAudioData(arrayBuffer);
        const wavBlob = encodeWAV(audioBuffer, 16000);
        const url = URL.createObjectURL(wavBlob);
        this.dom.audioPlayer.src = url;
        this.dom.audioPlayer.style.display = 'block';
        if (this.dom.audioPlayer.nextSibling !== this.dom.downloadLink) {
          this.dom.audioPlayer.parentNode.insertBefore(this.dom.downloadLink, this.dom.audioPlayer.nextSibling);
        }
        this.dom.downloadLink.href = url;
        this.dom.downloadLink.download = 'recording.wav';
        this.dom.downloadLink.textContent = 'Download Recording';
        this.dom.downloadLink.style.display = 'block';
        // Send to /identify endpoint
        this.dom.accentResult.style.display = 'block';
        this.dom.accentResult.textContent = 'Memprediksi aksen...';
        try {
          const formData = new FormData();
          formData.append('file', wavBlob, 'recording.wav');
          const response = await fetch('http://localhost:5000/identify', {
            method: 'POST',
            body: formData
          });
          if (!response.ok) throw new Error('Server error');
          const data = await response.json();
          if (data.predicted_accent && typeof data.us_confidence === 'number') {
            if (data.predicted_accent.toLowerCase() === 'us') {
              this.dom.accentResult.textContent = `✅ Selamat! Aksen Anda terdeteksi sebagai US dengan keyakinan ${data.us_confidence.toFixed(2)}%.`;
            } else {
              this.dom.accentResult.textContent = `❌ Aksen Anda terdeteksi sebagai ${data.predicted_accent} (keyakinan US: ${data.us_confidence.toFixed(2)}%). Cobalah berbicara dengan aksen US.`;
            }
          } else {
            this.dom.accentResult.textContent = 'Gagal membaca hasil prediksi aksen.';
          }
        } catch (err) {
          this.dom.accentResult.textContent = 'Prediksi aksen gagal.';
        }
      };
      // Setup waveform
      this.audioContext = new AudioContext();
      this.source = this.audioContext.createMediaStreamSource(this.stream);
      this.analyser = this.audioContext.createAnalyser();
      this.analyser.fftSize = 2048;
      this.dataArray = new Uint8Array(this.analyser.fftSize);
      this.source.connect(this.analyser);
      this.dom.waveform.style.display = 'block';
      this.drawWaveform();
      // UI
      this.dom.recordBtn.textContent = 'Stop Recording';
      this.dom.recordText.textContent = `Elden Ring isn’t just another action game—it’s a test of patience, timing, and strategy. You’ll die. A lot. But each defeat teaches you something. Mastering moves, reading enemy patterns, and learning when to strike or step back is key. It’s not about button-mashing—it’s about understanding the fight. Once you get it, every victory feels earned. This isn’t a game you play; it’s one you overcome.`;
      this.dom.recordText.style.display = 'block';
      this.dom.downloadLink.style.display = 'none';
      this.mediaRecorder.start();
    } catch (err) {
      this.dom.accentResult.style.display = 'block';
      this.dom.accentResult.textContent = 'Error accessing microphone.';
    }
  }

  drawWaveform() {
    this.analyser.getByteTimeDomainData(this.dataArray);
    const ctx = this.dom.waveform.getContext('2d');
    ctx.clearRect(0, 0, this.dom.waveform.width, this.dom.waveform.height);
    ctx.lineWidth = 2;
    ctx.strokeStyle = '#4f8cff';
    ctx.beginPath();
    let sliceWidth = this.dom.waveform.width / this.dataArray.length;
    let x = 0;
    for (let i = 0; i < this.dataArray.length; i++) {
      let v = this.dataArray[i] / 128.0;
      let y = v * this.dom.waveform.height / 2;
      if (i === 0) ctx.moveTo(x, y);
      else ctx.lineTo(x, y);
      x += sliceWidth;
    }
    ctx.lineTo(this.dom.waveform.width, this.dom.waveform.height / 2);
    ctx.stroke();
    this.animationId = requestAnimationFrame(() => this.drawWaveform());
  }
}
