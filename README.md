# Audio Recorder Vite App

This is a simple Vite + JavaScript (vanilla) frontend app. It features:

- A single page with a record button
- Waveform visualization during recording
- Noise suppression and echo cancellation
- Saves audio as a 16kHz WAV file
- Download link for the recording (saving to `recordings` folder requires backend support)
- Displays a motivational text when recording starts

## Usage

1. Click the **Start Recording** button to begin.
2. The motivational text and waveform will appear.
3. Click **Stop Recording** to finish.
4. Download the WAV file using the provided link.

## Note
- Saving directly to the `recordings` folder on the server requires backend support. Currently, the app provides a download link for the user.

## Project Setup

```
npm install
npm run dev
```

Open [http://localhost:5173](http://localhost:5173) in your browser.
