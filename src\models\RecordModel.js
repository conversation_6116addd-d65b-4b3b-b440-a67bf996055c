export class RecordModel {
  constructor() {
    this.state = {
      isRecording: false,
      audioChunks: [],
      predictedAccent: null,
      usConfidence: null,
      error: null,
    };
  }

  setRecording(isRecording) {
    this.state.isRecording = isRecording;
  }

  addAudioChunk(chunk) {
    this.state.audioChunks.push(chunk);
  }

  clearAudioChunks() {
    this.state.audioChunks = [];
  }

  setPrediction(predictedAccent, usConfidence) {
    this.state.predictedAccent = predictedAccent;
    this.state.usConfidence = usConfidence;
  }

  setError(error) {
    this.state.error = error;
  }

  reset() {
    this.state = {
      isRecording: false,
      audioChunks: [],
      predictedAccent: null,
      usConfidence: null,
      error: null,
    };
  }
}
