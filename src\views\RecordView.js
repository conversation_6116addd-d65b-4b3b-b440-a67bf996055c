export class RecordView {
  constructor() {
    this.state = {
      isRecording: false,
      hasAudio: false,
      isProcessing: false,
      error: null
    };
    this.observers = [];
  }

  // Observer pattern for state changes
  subscribe(observer) {
    this.observers.push(observer);
  }

  unsubscribe(observer) {
    this.observers = this.observers.filter(obs => obs !== observer);
  }

  notify(event, data) {
    this.observers.forEach(observer => {
      if (typeof observer[event] === 'function') {
        observer[event](data);
      }
    });
  }

  // State management methods
  setState(newState) {
    const prevState = { ...this.state };
    this.state = { ...this.state, ...newState };
    this.notify('onStateChange', { prevState, newState: this.state });
  }

  // UI state update methods
  onRecordingStart() {
    this.setState({
      isRecording: true,
      hasAudio: false,
      error: null,
      isProcessing: false
    });
    this.notify('onRecordingStart', this.state);
  }

  onRecordingStop() {
    this.setState({
      isRecording: false,
      isProcessing: true
    });
    this.notify('onRecordingStop', this.state);
  }

  onWaveformUpdate(waveformData) {
    this.notify('onWaveformUpdate', waveformData);
  }

  onAudioReady(audioData) {
    this.setState({
      hasAudio: true,
      isProcessing: false
    });
    this.notify('onAudioReady', audioData);
  }

  onAccentResult(result) {
    this.notify('onAccentResult', result);
  }

  onError(error) {
    this.setState({
      error: error.message || error,
      isRecording: false,
      isProcessing: false
    });
    this.notify('onError', error);
  }

  onReset() {
    this.setState({
      isRecording: false,
      hasAudio: false,
      isProcessing: false,
      error: null
    });
    this.notify('onReset', this.state);
  }

  // Utility methods for UI state queries
  canStartRecording() {
    return !this.state.isRecording && !this.state.isProcessing;
  }

  canStopRecording() {
    return this.state.isRecording;
  }

  isIdle() {
    return !this.state.isRecording && !this.state.isProcessing && !this.state.error;
  }
}
