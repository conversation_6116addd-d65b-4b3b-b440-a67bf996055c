import './styles/style.css';
import { RecordModel } from './models/RecordModel.js';
import { RecordView } from './views/RecordView.js';
import { RecordPresenter } from './presenters/RecordPresenter.js';

// DOM references
const domRefs = {
  recordBtn: document.getElementById('record-btn'),
  recordText: document.getElementById('record-text'),
  waveform: document.getElementById('waveform'),
  downloadLink: document.getElementById('download-link'),
  audioPlayer: document.getElementById('audio-player'),
  accentResult: document.getElementById('accent-result'),
};

// Instantiate MVP
const model = new RecordModel();
const view = new RecordView();
const presenter = new RecordPresenter(model, view, domRefs);

presenter.init();
